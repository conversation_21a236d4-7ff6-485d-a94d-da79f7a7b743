import  { useState, useEffect } from 'react';
import { Eye, Layers, Plus, Search, Target, Move, X, ArrowDown, Mountain, Navigation, Map, FileText, Menu, Settings, ChevronRight, ChevronLeft } from 'lucide-react';

interface MapControlsProps {
  mapLayers: {
    friendly: boolean;
    enemy: boolean;
    equipment: boolean;
    terrain: boolean;
    obstacles: boolean;
  };
  onToggleLayer: (layer: 'friendly' | 'enemy' | 'equipment' | 'terrain' | 'obstacles') => void;
  onZoom: (direction: 'in' | 'out') => void;
  onToggleSatelliteView: () => void;
  onToggleMapGallery: () => void;
  satelliteView: boolean;
  targetingMode: boolean;
  moveMode: boolean;
  onToggleTargetingMode: () => void;
  onToggleMoveMode: () => void;
  onSpatialAnalysisClick: () => void;
  onAnalyzeCirclesSingle: () => void;
  onAnalyzeCirclesAll: () => void;
  onAnalyzeCirclesSingleWithTerrain: () => void;
  onAnalyzeCirclesSingleWithObstacles: () => void;
  onOpenCircleConfig: () => void;
  onEditCirclesSingleWithObstacles: () => void;
  onEditCirclesAllWithObstacles: () => void;
  // Nuevas propiedades para análisis espacial avanzado
  onToggleTerrainAnalysis: () => void;
  onToggleRouteAnalysis: () => void;
  showTerrainAnalysis: boolean;
  showRouteAnalysis: boolean;
  // Nuevas propiedades para simulación de combate y planificación de escenarios
  onOpenBattleSimulator: () => void;
  onOpenScenarioPlanner: () => void;
  // Advanced Battle Simulation
  battleSelectionMode?: boolean;
  selectedFriendlyUnits?: string[];
  selectedEnemyUnits?: string[];
  onToggleBattleSelectionMode?: () => void;
  onStartAdvancedBattleSimulation?: () => void;
  onClearUnitSelections?: () => void;
}

type MenuSection = 'main' | 'basic' | 'spatial' | 'circles' | 'simulation';

export default function MapControls({
  mapLayers,
  onToggleLayer,
  onZoom,
  onToggleSatelliteView,
  onToggleMapGallery,
  satelliteView,
  targetingMode,
  moveMode,
  onToggleTargetingMode,
  onToggleMoveMode,
  onSpatialAnalysisClick,
  onAnalyzeCirclesSingle,
  onAnalyzeCirclesAll,
  onAnalyzeCirclesSingleWithTerrain,
  onAnalyzeCirclesSingleWithObstacles,
  onOpenCircleConfig,
  onEditCirclesSingleWithObstacles,
  onEditCirclesAllWithObstacles,
  // Nuevas propiedades para análisis espacial avanzado
  onToggleTerrainAnalysis,
  onToggleRouteAnalysis,
  showTerrainAnalysis,
  showRouteAnalysis,
  // Nuevas propiedades para simulación de combate y planificación de escenarios
  onOpenBattleSimulator,
  onOpenScenarioPlanner,
  // Advanced Battle Simulation
  battleSelectionMode = false,
  selectedFriendlyUnits = [],
  selectedEnemyUnits = [],
  onToggleBattleSelectionMode,
  onStartAdvancedBattleSimulation,
  onClearUnitSelections
}: MapControlsProps) {
  const [showLayerControls, setShowLayerControls] = useState(true);
  const [showActionPanel, setShowActionPanel] = useState(false);
  const [currentMenu, setCurrentMenu] = useState<'main' | 'basic' | 'spatial' | 'circles' | 'simulation'>('main');

  const handleMenuItemClick = (action: () => void) => {
    action();
    setShowActionPanel(false);
    setCurrentMenu('main');
  };

  const renderMainMenu = () => (
    <div className="p-4">
      <div className="space-y-3">
        <button
          onClick={() => setCurrentMenu('basic')}
          className="w-full flex items-center justify-between rounded-md px-4 py-3 shadow-sm bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors"
        >
          <div className="flex items-center">
            <Target size={20} className="ml-3" />
            <span className="text-base font-medium">العمليات الأساسية</span>
          </div>
          <ChevronLeft size={20} />
        </button>

        <button
          onClick={() => setCurrentMenu('spatial')}
          className="w-full flex items-center justify-between rounded-md px-4 py-3 shadow-sm bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors"
        >
          <div className="flex items-center">
            <ArrowDown size={20} className="ml-3" />
            <span className="text-base font-medium">التحليل المكاني</span>
          </div>
          <ChevronLeft size={20} />
        </button>

        <button
          onClick={() => setCurrentMenu('circles')}
          className="w-full flex items-center justify-between rounded-md px-4 py-3 shadow-sm bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors"
        >
          <div className="flex items-center">
            <Search size={20} className="ml-3" />
            <span className="text-base font-medium">تحليل الدوائر</span>
          </div>
          <ChevronLeft size={20} />
        </button>

        <button
          onClick={() => setCurrentMenu('simulation')}
          className="w-full flex items-center justify-between rounded-md px-4 py-3 shadow-sm bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors"
        >
          <div className="flex items-center">
            <Map size={20} className="ml-3" />
            <span className="text-base font-medium">المحاكاة والتخطيط</span>
          </div>
          <ChevronLeft size={20} />
        </button>
      </div>
    </div>
  );

  const renderBasicOperationsMenu = () => (
    <div className="p-4">
      <div className="flex items-center mb-4">
        <button
          onClick={() => setCurrentMenu('main')}
          className="flex items-center text-blue-600 hover:text-blue-800"
        >
          <ChevronRight size={20} className="ml-1" />
          <span>العودة</span>
        </button>
        <h3 className="text-lg font-bold text-gray-800 mr-4">العمليات الأساسية</h3>
      </div>
      <div className="space-y-3">
        <button
          onClick={() => handleMenuItemClick(onToggleTargetingMode)}
          className={`w-full flex items-center rounded-md px-4 py-3 shadow-sm transition-colors ${
            targetingMode ? 'bg-red-600 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
        >
          <Target size={20} className="ml-3" />
          <span className="text-base">{targetingMode ? 'إلغاء الاستهداف' : 'وضع الاستهداف'}</span>
        </button>
        
        <button
          onClick={() => handleMenuItemClick(onToggleMoveMode)}
          className={`w-full flex items-center rounded-md px-4 py-3 shadow-sm transition-colors ${
            moveMode ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
        >
          <Move size={20} className="ml-3" />
          <span className="text-base">{moveMode ? 'إلغاء التحريك' : 'تحريك القوات'}</span>
        </button>
      </div>
    </div>
  );

  const renderSpatialAnalysisMenu = () => (
    <div className="p-4">
      <div className="flex items-center mb-4">
        <button
          onClick={() => setCurrentMenu('main')}
          className="flex items-center text-blue-600 hover:text-blue-800"
        >
          <ChevronRight size={20} className="ml-1" />
          <span>العودة</span>
        </button>
        <h3 className="text-lg font-bold text-gray-800 mr-4">التحليل المكاني</h3>
      </div>
      <div className="space-y-3">
        <button
          onClick={() => handleMenuItemClick(onSpatialAnalysisClick)}
          className="w-full flex items-center rounded-md px-4 py-3 shadow-sm bg-yellow-500 text-white hover:bg-yellow-600 transition-colors"
        >
          <ArrowDown size={20} className="ml-3" />
          <span className="text-base">تحليل مكاني للقوات</span>
        </button>
        
        <button
          onClick={() => handleMenuItemClick(onToggleTerrainAnalysis)}
          className={`w-full flex items-center rounded-md px-4 py-3 shadow-sm transition-colors ${
            showTerrainAnalysis ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
        >
          <Mountain size={20} className="ml-3" />
          <span className="text-base">تحليل التضاريس</span>
        </button>
        
        <button
          onClick={() => handleMenuItemClick(onToggleRouteAnalysis)}
          className={`w-full flex items-center rounded-md px-4 py-3 shadow-sm transition-colors ${
            showRouteAnalysis ? 'bg-emerald-600 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
        >
          <Navigation size={20} className="ml-3" />
          <span className="text-base">تحليل المسارات</span>
        </button>
        
        <button
          onClick={() => handleMenuItemClick(onOpenCircleConfig)}
          className="w-full flex items-center rounded-md px-4 py-3 shadow-sm bg-gray-700 text-white hover:bg-gray-800 transition-colors"
        >
          <Settings size={20} className="ml-3" />
          <span className="text-base">تهيئة أبعاد الدوائر</span>
        </button>
      </div>
    </div>
  );

  const renderCircleAnalysisMenu = () => (
    <div className="p-4">
      <div className="flex items-center mb-4">
        <button
          onClick={() => setCurrentMenu('main')}
          className="flex items-center text-blue-600 hover:text-blue-800"
        >
          <ChevronRight size={20} className="ml-1" />
          <span>العودة</span>
        </button>
        <h3 className="text-lg font-bold text-gray-800 mr-4">تحليل الدوائر</h3>
      </div>
      <div className="space-y-3">
        <button
          onClick={() => handleMenuItemClick(onAnalyzeCirclesSingle)}
          className="w-full flex items-center rounded-md px-4 py-3 shadow-sm bg-green-500 text-white hover:bg-green-600 transition-colors"
        >
          <Search size={20} className="ml-3" />
          <span className="text-base">تحليل الدوائر (وحدة)</span>
        </button>
        
        <button
          onClick={() => handleMenuItemClick(onAnalyzeCirclesAll)}
          className="w-full flex items-center rounded-md px-4 py-3 shadow-sm bg-yellow-600 text-white hover:bg-yellow-700 transition-colors"
        >
          <Search size={20} className="ml-3" />
          <span className="text-base">تحليل الدوائر (الكل)</span>
        </button>
        
        <button
          onClick={() => handleMenuItemClick(onAnalyzeCirclesSingleWithTerrain)}
          className="w-full flex items-center rounded-md px-4 py-3 shadow-sm bg-orange-600 text-white hover:bg-orange-700 transition-colors"
        >
          <Search size={20} className="ml-3" />
          <span className="text-base">تحليل الدوائر مع التضاريس</span>
        </button>
        
        <button
          onClick={() => handleMenuItemClick(onAnalyzeCirclesSingleWithObstacles)}
          className="w-full flex items-center rounded-md px-4 py-3 shadow-sm bg-purple-600 text-white hover:bg-purple-700 transition-colors"
        >
          <Search size={20} className="ml-3" />
          <span className="text-base">تحليل الدوائر مع العوائق</span>
        </button>
      </div>
    </div>
  );

  const renderSimulationMenu = () => (
    <div className="p-4">
      <div className="flex items-center mb-4">
        <button
          onClick={() => setCurrentMenu('main')}
          className="flex items-center text-blue-600 hover:text-blue-800"
        >
          <ChevronRight size={20} className="ml-1" />
          <span>العودة</span>
        </button>
        <h3 className="text-lg font-bold text-gray-800 mr-4">المحاكاة والتخطيط</h3>
      </div>
      <div className="space-y-3">
        <button
          onClick={() => handleMenuItemClick(onOpenBattleSimulator)}
          className="w-full flex items-center rounded-md px-4 py-3 shadow-sm bg-red-500 text-white hover:bg-red-600 transition-colors"
        >
          <Map size={20} className="ml-3" />
          <span className="text-base">محاكاة المعركة</span>
        </button>

        {/* Advanced Battle Simulation */}
        <div className="border-t pt-3">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">محاكاة متقدمة</h4>

          <button
            onClick={() => {
              if (onToggleBattleSelectionMode) {
                handleMenuItemClick(onToggleBattleSelectionMode);
              }
            }}
            className={`w-full flex items-center rounded-md px-4 py-3 shadow-sm transition-colors ${
              battleSelectionMode
                ? 'bg-orange-500 text-white hover:bg-orange-600'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
          >
            <Target size={20} className="ml-3" />
            <span className="text-base">
              {battleSelectionMode ? 'إلغاء اختيار الوحدات' : 'اختيار وحدات للمعركة'}
            </span>
          </button>

          {battleSelectionMode && (
            <div className="mt-2 p-3 bg-gray-50 rounded-md">
              <div className="text-xs text-gray-600 mb-2">
                <div>وحدات صديقة: {selectedFriendlyUnits.length}</div>
                <div>وحدات معادية: {selectedEnemyUnits.length}</div>
              </div>

              <div className="flex space-x-2 space-x-reverse">
                <button
                  onClick={() => {
                    if (onStartAdvancedBattleSimulation) {
                      handleMenuItemClick(onStartAdvancedBattleSimulation);
                    }
                  }}
                  disabled={selectedFriendlyUnits.length === 0 || selectedEnemyUnits.length === 0}
                  className="flex-1 bg-green-500 text-white px-3 py-2 rounded text-sm hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  🚀 بدء المحاكاة
                </button>

                <button
                  onClick={() => {
                    if (onClearUnitSelections) {
                      handleMenuItemClick(onClearUnitSelections);
                    }
                  }}
                  className="flex-1 bg-gray-500 text-white px-3 py-2 rounded text-sm hover:bg-gray-600"
                >
                  مسح الاختيار
                </button>
              </div>
            </div>
          )}
        </div>
        
        <button
          onClick={() => handleMenuItemClick(onOpenScenarioPlanner)}
          className="w-full flex items-center rounded-md px-4 py-3 shadow-sm bg-blue-500 text-white hover:bg-blue-600 transition-colors"
        >
          <FileText size={20} className="ml-3" />
          <span className="text-base">مخطط السيناريوهات</span>
        </button>
      </div>
    </div>
  );

  const getCurrentMenuContent = () => {
    switch (currentMenu) {
      case 'basic':
        return renderBasicOperationsMenu();
      case 'spatial':
        return renderSpatialAnalysisMenu();
      case 'circles':
        return renderCircleAnalysisMenu();
      case 'simulation':
        return renderSimulationMenu();
      default:
        return renderMainMenu();
    }
  };

  return (
    <>
      {/* Map Controls */}
      <div className="absolute top-4 left-4 z-[9999] flex flex-col space-y-2" style={{zIndex: 9999}}>
        <button
          onClick={() => onZoom('in')}
          className="rounded-md bg-white p-2 shadow-md hover:bg-gray-100"
          title="تكبير"
        >
          <Plus size={20} />
        </button>
        <button
          onClick={() => onZoom('out')}
          className="rounded-md bg-white p-2 shadow-md hover:bg-gray-100"
          title="تصغير"
        >
          <Layers size={20} />
        </button>
        <button
          onClick={onToggleSatelliteView}
          className={`rounded-md p-2 shadow-md ${satelliteView ? 'bg-blue-500 text-white' : 'bg-white hover:bg-gray-100'}`}
          title="عرض الأقمار الصناعية"
        >
          <Eye size={20} />
        </button>
        <button
          onClick={onToggleMapGallery}
          className="rounded-md bg-white p-2 shadow-md hover:bg-gray-100"
          title="صور إضافية"
        >
          <Layers size={20} />
        </button>
      </div>
      
      {/* Layer Controls */}
      <div className="absolute top-4 right-4 z-[9999] rounded-md bg-white shadow-md" style={{zIndex: 9999}}>
        <div className="flex items-center justify-between border-b px-4 py-2">
          <div className="text-sm font-medium">طبقات الخريطة</div>
          <button 
            onClick={() => setShowLayerControls(!showLayerControls)}
            className="rounded-full p-1 hover:bg-gray-100"
          >
            {showLayerControls ? <X size={16} /> : <Layers size={16} />}
          </button>
        </div>
        
        {showLayerControls && (
          <div className="space-y-2 p-3">
            <label className="flex cursor-pointer items-center">
              <input
                type="checkbox"
                checked={mapLayers.friendly}
                onChange={() => onToggleLayer('friendly')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600"
              />
              <span className="mr-2 text-sm">القوات الصديقة</span>
            </label>
            <label className="flex cursor-pointer items-center">
              <input
                type="checkbox"
                checked={mapLayers.enemy}
                onChange={() => onToggleLayer('enemy')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600"
              />
              <span className="mr-2 text-sm">قوات العدو</span>
            </label>
            <label className="flex cursor-pointer items-center">
              <input
                type="checkbox"
                checked={mapLayers.equipment}
                onChange={() => onToggleLayer('equipment')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600"
              />
              <span className="mr-2 text-sm">العتاد</span>
            </label>
            <label className="flex cursor-pointer items-center">
              <input
                type="checkbox"
                checked={mapLayers.terrain}
                onChange={() => onToggleLayer('terrain')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600"
              />
              <span className="mr-2 text-sm">التضاريس</span>
            </label>
            <label className="flex cursor-pointer items-center">
              <input
                type="checkbox"
                checked={mapLayers.obstacles}
                onChange={() => onToggleLayer('obstacles')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600"
              />
              <span className="mr-2 text-sm">العوائق</span>
            </label>
          </div>
        )}
      </div>
      
      {/* زر عرض القائمة - في أقصى أسفل يسار الشاشة */}
      <div className="absolute bottom-4 left-4 z-[9999]" style={{zIndex: 9999}}>
        <button
          onClick={() => {
            setShowActionPanel(!showActionPanel);
            setCurrentMenu('main');
          }}
          className={`flex items-center rounded-full p-3 shadow-lg transition-all duration-300 ${
            showActionPanel 
              ? 'bg-blue-600 text-white' 
              : 'bg-white text-gray-800 hover:bg-gray-100'
          }`}
          title="عرض أدوات الخريطة"
        >
          <Menu size={24} />
        </button>
      </div>

      {/* لوحة الأدوات المتدرجة */}
      {showActionPanel && (
        <div className="absolute bottom-20 left-4 z-[9999] bg-white rounded-lg shadow-xl border max-h-96 overflow-y-auto" style={{zIndex: 9999, width: '320px'}}>
          <div className="flex items-center justify-between border-b px-4 py-3 bg-gray-50 rounded-t-lg">
            <h3 className="text-lg font-bold text-gray-800">أدوات الخريطة الميدانية</h3>
            <button 
              onClick={() => {
                setShowActionPanel(false);
                setCurrentMenu('main');
              }}
              className="rounded-full p-1 hover:bg-gray-200 text-gray-600"
            >
              <X size={20} />
            </button>
          </div>
          
          {getCurrentMenuContent()}
        </div>
      )}
    </>
  );
}
 