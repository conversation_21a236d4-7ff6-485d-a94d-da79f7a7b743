import React, { useState, useEffect } from 'react';

interface BattleResultFlashProps {
  result: {
    winner: 'friendly' | 'enemy' | 'draw';
    friendlyLosses: number;
    enemyLosses: number;
    duration: number;
    summary: string;
  };
  onClose: () => void;
}

export default function BattleResultFlash({ result, onClose }: BattleResultFlashProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  useEffect(() => {
    // إظهار الرسالة مع تأثير
    setTimeout(() => setIsVisible(true), 100);
    
    // إخفاء الرسالة تلقائياً بعد 8 ثوان
    const autoClose = setTimeout(() => {
      handleClose();
    }, 8000);

    return () => clearTimeout(autoClose);
  }, []);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300);
    }, 300);
  };

  const getResultIcon = () => {
    switch (result.winner) {
      case 'friendly':
        return '🎉';
      case 'enemy':
        return '💥';
      default:
        return '⚖️';
    }
  };

  const getResultColor = () => {
    switch (result.winner) {
      case 'friendly':
        return 'from-success to-success/80';
      case 'enemy':
        return 'from-error to-error/80';
      default:
        return 'from-warning to-warning/80';
    }
  };

  const getResultTitle = () => {
    switch (result.winner) {
      case 'friendly':
        return 'انتصار!';
      case 'enemy':
        return 'هزيمة';
      default:
        return 'تعادل';
    }
  };

  return (
    <div className={`fixed inset-0 z-[10000] flex items-center justify-center p-4 transition-all duration-500 ${
      isVisible && !isClosing ? 'bg-black/50 backdrop-blur-sm' : 'bg-transparent pointer-events-none'
    }`}>
      <div className={`relative max-w-md w-full transition-all duration-500 transform ${
        isVisible && !isClosing 
          ? 'scale-100 opacity-100 translate-y-0' 
          : 'scale-75 opacity-0 translate-y-8'
      }`}>
        {/* الخلفية المتدرجة */}
        <div className={`absolute inset-0 bg-gradient-to-br ${getResultColor()} rounded-2xl blur-xl opacity-60`}></div>
        
        {/* المحتوى الرئيسي */}
        <div className="relative bg-white/95 backdrop-blur-lg rounded-2xl shadow-2xl border border-white/20 overflow-hidden">
          {/* الشريط العلوي */}
          <div className={`bg-gradient-to-r ${getResultColor()} p-4`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 space-x-reverse">
                <span className="text-3xl">{getResultIcon()}</span>
                <h2 className="text-2xl font-bold text-white font-heliopolis">
                  {getResultTitle()}
                </h2>
              </div>
              <button
                onClick={handleClose}
                className="text-white/80 hover:text-white transition-colors rounded-full p-1 hover:bg-white/20"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* المحتوى */}
          <div className="p-6">
            <p className="text-lg font-heliopolis text-neutral-80 mb-6 text-center">
              {result.summary}
            </p>

            {/* الإحصائيات */}
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <div className="bg-gradient-to-br from-primary-500/10 to-primary-500/5 rounded-xl p-4">
                  <div className="text-2xl font-bold text-primary-600 font-heliopolis">
                    {Math.floor(result.duration / 60)}:{(result.duration % 60).toString().padStart(2, '0')}
                  </div>
                  <div className="text-sm text-neutral-60 font-heliopolis">مدة المعركة</div>
                </div>
              </div>
              
              <div className="text-center">
                <div className="bg-gradient-to-br from-success/10 to-success/5 rounded-xl p-4">
                  <div className="text-2xl font-bold text-success font-heliopolis">
                    {result.friendlyLosses}%
                  </div>
                  <div className="text-sm text-neutral-60 font-heliopolis">خسائر قواتنا</div>
                </div>
              </div>
              
              <div className="text-center">
                <div className="bg-gradient-to-br from-error/10 to-error/5 rounded-xl p-4">
                  <div className="text-2xl font-bold text-error font-heliopolis">
                    {result.enemyLosses}%
                  </div>
                  <div className="text-sm text-neutral-60 font-heliopolis">خسائر العدو</div>
                </div>
              </div>
            </div>

            {/* شريط التقدم للإغلاق التلقائي */}
            <div className="mb-4">
              <div className="w-full bg-neutral-20 rounded-full h-1">
                <div 
                  className={`bg-gradient-to-r ${getResultColor()} h-1 rounded-full transition-all duration-100 animate-pulse`}
                  style={{
                    animation: 'shrink 8s linear forwards'
                  }}
                ></div>
              </div>
              <p className="text-xs text-neutral-50 text-center mt-2 font-heliopolis">
                سيتم الإغلاق تلقائياً...
              </p>
            </div>

            {/* زر الإغلاق */}
            <button
              onClick={handleClose}
              className="w-full bg-gradient-primary text-white py-3 rounded-xl font-heliopolis font-medium shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
            >
              إغلاق
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes shrink {
          from {
            width: 100%;
          }
          to {
            width: 0%;
          }
        }
      `}</style>
    </div>
  );
}
