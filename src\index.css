/* HT Heliopolis Font Import */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

@tailwind  base;
@tailwind components;
@tailwind utilities;

/* Base font family */
* {
  font-family: 'Cairo', 'HT Heliopolis', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* IBM Color Library Inspired Color Palette */
:root {
  /* Primary Colors */
  --primary-blue: #0f62fe;
  --primary-blue-light: #4589ff;
  --primary-blue-dark: #002d9c;

  /* Secondary Colors */
  --secondary-teal: #009d9a;
  --secondary-teal-light: #1192e8;
  --secondary-teal-dark: #005d5d;

  /* Neutral Colors */
  --neutral-white: #ffffff;
  --neutral-gray-10: #f4f4f4;
  --neutral-gray-20: #e0e0e0;
  --neutral-gray-30: #c6c6c6;
  --neutral-gray-40: #a8a8a8;
  --neutral-gray-50: #8d8d8d;
  --neutral-gray-60: #6f6f6f;
  --neutral-gray-70: #525252;
  --neutral-gray-80: #393939;
  --neutral-gray-90: #262626;
  --neutral-gray-100: #161616;

  /* Status Colors */
  --success-green: #24a148;
  --warning-yellow: #f1c21b;
  --error-red: #da1e28;
  --info-blue: #4589ff;

  /* Gradient Colors */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-teal) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--neutral-gray-10) 0%, var(--neutral-white) 100%);
  --gradient-dark: linear-gradient(135deg, var(--neutral-gray-90) 0%, var(--neutral-gray-100) 100%);
}

/* Equipment and unit icon styles */
.unit-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.unit-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  border: 2px solid white;
  transition: all 0.3s ease;
}

.unit-icon.friendly {
  background-color: #3b82f6;
}

.unit-icon.enemy {
  background-color: #ef4444;
}

/* أنماط الوحدات المختارة للمعركة */
.unit-icon.selected-unit {
  background-color: #10b981; /* أخضر */
  width: 32px;
  height: 32px;
  border: 3px solid white;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.8);
  z-index: 1000;
}

.unit-icon.target-unit {
  background-color: #f59e0b; /* برتقالي */
  width: 32px;
  height: 32px;
  border: 3px solid white;
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.8);
  z-index: 1000;
}

.unit-icon.advanced-battle-selected {
  background: linear-gradient(135deg, #8b5cf6, #3b82f6); /* تدرج بنفسجي-أزرق */
  width: 40px;
  height: 40px;
  border: 4px solid white;
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.9);
  z-index: 1000;
  animation: pulse-advanced-battle 1.5s infinite;
}

@keyframes pulse-advanced-battle {
  0% {
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.9);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 25px rgba(139, 92, 246, 1);
    transform: scale(1.1);
  }
  100% {
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.9);
    transform: scale(1);
  }
}

.unit-icon.battle-active {
  background: linear-gradient(135deg, #ef4444, #f97316, #eab308); /* تدرج أحمر-برتقالي-أصفر */
  width: 45px;
  height: 45px;
  border: 5px solid white;
  box-shadow: 0 0 20px rgba(239, 68, 68, 1);
  z-index: 1000;
  animation: battle-active 0.8s infinite;
}

@keyframes battle-active {
  0% {
    box-shadow: 0 0 20px rgba(239, 68, 68, 1);
    transform: scale(1);
    background: linear-gradient(135deg, #ef4444, #f97316, #eab308);
  }
  25% {
    box-shadow: 0 0 30px rgba(249, 115, 22, 1);
    transform: scale(1.15);
    background: linear-gradient(135deg, #f97316, #eab308, #ef4444);
  }
  50% {
    box-shadow: 0 0 35px rgba(234, 179, 8, 1);
    transform: scale(1.2);
    background: linear-gradient(135deg, #eab308, #ef4444, #f97316);
  }
  75% {
    box-shadow: 0 0 30px rgba(249, 115, 22, 1);
    transform: scale(1.15);
    background: linear-gradient(135deg, #f97316, #eab308, #ef4444);
  }
  100% {
    box-shadow: 0 0 20px rgba(239, 68, 68, 1);
    transform: scale(1);
    background: linear-gradient(135deg, #ef4444, #f97316, #eab308);
  }
}

/* تأثيرات النبض للوحدات المختارة */
.unit-icon-container.battle-selected .unit-icon {
  animation: pulse-selected 2s infinite;
}

.unit-icon-container.battle-target .unit-icon {
  animation: pulse-target 2s infinite;
}

@keyframes pulse-selected {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

@keyframes pulse-target {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

.equipment-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  border: 2px solid white;
}

.equipment-icon-container.friendly {
  background-color: #14b8a6;
}

.equipment-icon-container.enemy {
  background-color: #f97316;
}

/* Animation elements */
.targeting-line {
  stroke: red;
  stroke-width: 2;
  stroke-dasharray: 5;
  animation: dash 1s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

.missile-animation {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: red;
  box-shadow: 0 0 10px red, 0 0 5px orange;
  z-index: 1000;
  transform: translate(-50%, -50%);
}

.explosion-animation {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,165,0,0.8) 0%, rgba(255,0,0,0.8) 50%, rgba(0,0,0,0) 100%);
  z-index: 1000;
  transform: translate(-50%, -50%);
  animation: explode 1.5s ease-out forwards;
}

@keyframes explode {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* Right-to-left support */
body {
  direction: rtl;
}

/* Leaflet Map fixes */
.leaflet-container {
  height: 100%;
  width: 100%;
  direction: ltr; /* Keep map controls in LTR */
}

.leaflet-popup-content {
  direction: rtl;
  text-align: right;
}

/* Fix for equipment display */
.leaflet-div-icon {
  background: transparent;
  border: none;
}

/* Loader */
.loader {
  width: 48px;
  height: 48px;
  border: 5px solid #3b82f6;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Pulse animation for target markers */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 128, 0, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(0, 128, 0, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 128, 0, 0);
  }
}

/* Fix z-index for map controls to prevent disappearing */
.leaflet-top, 
.leaflet-bottom {
  z-index: 400 !important;
}

.map-controls {
  z-index: 1000 !important;
}

/* Ensure map tools stay visible */
.map-tools-container {
  z-index: 500 !important;
  pointer-events: auto !important;
}

/* Add transitions for smoother UI */
.layer-controls-panel {
  transition: all 0.3s ease;
}

/* Make sure search results appear above map */
.search-results-container {
  z-index: 1000 !important;
}

/* Ensure all popups have proper stacking context */
.leaflet-popup {
  z-index: 700 !important;
}

/* فلاش دائري للوحدات المهددة */
.pulse-threat {
  position: relative;
}
.pulse-blue::before, .pulse-red::before {
  content: '';
  position: absolute;
  left: 50%; top: 50%;
  width: 40px; height: 40px;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  z-index: -1;
  animation: pulse-threat-anim 1.2s infinite;
  opacity: 0.6;
}
.pulse-blue::before {
  background: radial-gradient(circle, #3b82f6 40%, transparent 70%);
}
.pulse-red::before {
  background: radial-gradient(circle, #ef4444 40%, transparent 70%);
}
@keyframes pulse-threat-anim {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.7; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.3; }
  100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.7; }
}
 