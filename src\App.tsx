import  { useState } from 'react';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import MapView from './components/MapView';
import UnitList from './components/UnitList';
import EquipmentList from './components/EquipmentList';
import AITargetingSystem from './components/AITargetingSystem';
import BattleScenarioPlanner from './components/BattleScenarioPlanner';
import TopBar from './components/TopBar';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');

  // Function to render the active component based on the selected tab
  const renderActiveComponent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />;
      case 'map':
        return <MapView />;
      case 'friendly':
        return <UnitList side="friendly" />;
      case 'enemy':
        return <UnitList side="enemy" />;
      case 'equipment':
        return <EquipmentList />;
      case 'analysis':
        return <AITargetingSystem onAttackExecuted={() => {}} />;
      case 'battleplanner':
        return <BattleScenarioPlanner onScenarioExecuted={() => {}} />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="flex h-screen overflow-hidden bg-gradient-secondary font-heliopolis">
      <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />

      <div className="flex flex-1 flex-col overflow-hidden">
        <TopBar activeTab={activeTab} />

        <main className="flex-1 overflow-hidden">
          <div className="h-full">
            {renderActiveComponent()}
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;
 