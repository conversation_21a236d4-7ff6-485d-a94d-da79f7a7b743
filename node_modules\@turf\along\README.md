# @turf/along

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## along

Takes a [LineString][1] and returns a [Point][2] at a specified distance along the line.

### Parameters

*   `line` **([Feature][3]<[LineString][1]> | [LineString][1])** input line
*   `distance` **[number][4]** distance along the line
*   `options` **[Object][5]?** Optional parameters (optional, default `{}`)

    *   `options.units` **Units** can be degrees, radians, miles, or kilometers (optional, default `"kilometers"`)

### Examples

```javascript
var line = turf.lineString([[-83, 30], [-84, 36], [-78, 41]]);
var options = {units: 'miles'};

var along = turf.along(line, 200, options);

//addToMap
var addToMap = [along, line]
```

Returns **[Feature][3]<[Point][2]>** Point `distance` `units` along the line

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/along
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
