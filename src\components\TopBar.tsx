import  { useState } from 'react';

interface TopBarProps {
  activeTab: string;
}

function Bell(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" />
      <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" />
    </svg>
  );
}

function Search(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <circle cx="11" cy="11" r="8" />
      <path d="m21 21-4.3-4.3" />
    </svg>
  );
}

function AlertCircle(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <circle cx="12" cy="12" r="10" />
      <line x1="12" y1="8" x2="12" y2="12" />
      <line x1="12" y1="16" x2="12.01" y2="16" />
    </svg>
  );
}

function User(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    </svg>
  );
}

export default function TopBar({ activeTab }: TopBarProps) {
  const [showNotifications, setShowNotifications] = useState(false);
  const [notificationCount, setNotificationCount] = useState(3);
  
  const getTabTitle = () => {
    switch (activeTab) {
      case 'dashboard':
        return 'لوحة المعلومات';
      case 'map':
        return 'الخريطة الميدانية';
      case 'friendly':
        return 'القوات الصديقة';
      case 'enemy':
        return 'قوات العدو';
      case 'equipment':
        return 'العتاد العسكري';
      case 'analysis':
        return 'تحليل الذكاء الاصطناعي';
      default:
        return 'نظام العمليات';
    }
  };
  
  return (
    <div className="flex h-16 items-center justify-between border-b border-neutral-20 bg-gradient-secondary px-6 shadow-lg backdrop-blur-sm">
      <div className="flex items-center">
        <h1 className="text-2xl font-bold font-heliopolis text-neutral-90 bg-gradient-primary bg-clip-text text-transparent">
          {getTabTitle()}
        </h1>
      </div>

      <div className="flex items-center space-x-4 space-x-reverse">
        <div className="relative hidden md:block">
          <input
            type="text"
            placeholder="بحث..."
            className="rounded-xl border-2 border-neutral-30 bg-white/80 py-2 pr-10 pl-4 font-heliopolis backdrop-blur-sm transition-all duration-200 focus:border-primary-500 focus:bg-white focus:outline-none focus:ring-2 focus:ring-primary-500/20"
          />
          <Search className="absolute inset-y-0 right-3 my-auto h-5 w-5 text-neutral-50" />
        </div>
        
        <div className="relative">
          <button
            onClick={() => setShowNotifications(!showNotifications)}
            className="relative rounded-xl p-2 transition-all duration-200 hover:bg-white/20 hover:shadow-lg"
          >
            <Bell className="h-6 w-6 text-neutral-70" />
            {notificationCount > 0 && (
              <span className="absolute -top-1 -right-1 flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-r from-error to-warning text-xs font-bold text-white shadow-lg animate-pulse">
                {notificationCount}
              </span>
            )}
          </button>
          
          {showNotifications && (
            <div className="absolute left-0 top-full mt-3 w-80 rounded-2xl border border-neutral-20 bg-white/95 p-4 shadow-2xl backdrop-blur-lg">
              <h3 className="mb-3 border-b border-neutral-20 pb-2 font-bold font-heliopolis text-neutral-90">التنبيهات</h3>
              <div className="space-y-3">
                <div className="flex rounded-xl bg-gradient-to-r from-warning/10 to-warning/5 p-3 transition-all duration-200 hover:shadow-md">
                  <AlertCircle className="mt-1 h-5 w-5 flex-shrink-0 text-warning" />
                  <div className="mr-3">
                    <p className="text-sm font-medium font-heliopolis text-neutral-90">تم رصد تحركات غير اعتيادية في القطاع الشمالي</p>
                    <span className="text-xs text-neutral-60">منذ 20 دقيقة</span>
                  </div>
                </div>
                <div className="flex rounded-xl bg-gradient-to-r from-error/10 to-error/5 p-3 transition-all duration-200 hover:shadow-md">
                  <AlertCircle className="mt-1 h-5 w-5 flex-shrink-0 text-error" />
                  <div className="mr-3">
                    <p className="text-sm font-medium font-heliopolis text-neutral-90">انخفاض مستوى الجاهزية للواء 35</p>
                    <span className="text-xs text-neutral-60">منذ ساعة</span>
                  </div>
                </div>
                <div className="flex rounded-xl bg-gradient-to-r from-info/10 to-info/5 p-3 transition-all duration-200 hover:shadow-md">
                  <AlertCircle className="mt-1 h-5 w-5 flex-shrink-0 text-info" />
                  <div className="mr-3">
                    <p className="text-sm font-medium font-heliopolis text-neutral-90">تحديث للتقارير الاستخباراتية في القطاع الشرقي</p>
                    <span className="text-xs text-neutral-60">منذ 3 ساعات</span>
                  </div>
                </div>
              </div>
              <button
                onClick={() => setNotificationCount(0)}
                className="mt-4 w-full rounded-xl bg-gradient-primary p-2 text-sm font-medium font-heliopolis text-white transition-all duration-200 hover:shadow-lg hover:scale-105"
              >
                تحديد الكل كمقروء
              </button>
            </div>
          )}
        </div>
        
        <div className="h-10 w-10 overflow-hidden rounded-full bg-gradient-primary shadow-lg ring-2 ring-white/50 transition-all duration-200 hover:scale-110 hover:shadow-xl">
          <User className="h-full w-full p-2 text-white" />
        </div>
      </div>
    </div>
  );
}
 