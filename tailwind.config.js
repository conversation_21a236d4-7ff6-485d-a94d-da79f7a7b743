/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        'heliopolis': ['Cairo', 'HT Heliopolis', 'sans-serif'],
      },
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#0f62fe',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#002d9c',
        },
        secondary: {
          50: '#f0fdfa',
          100: '#ccfbf1',
          200: '#99f6e4',
          300: '#5eead4',
          400: '#2dd4bf',
          500: '#009d9a',
          600: '#0891b2',
          700: '#0e7490',
          800: '#155e75',
          900: '#005d5d',
        },
        neutral: {
          10: '#f4f4f4',
          20: '#e0e0e0',
          30: '#c6c6c6',
          40: '#a8a8a8',
          50: '#8d8d8d',
          60: '#6f6f6f',
          70: '#525252',
          80: '#393939',
          90: '#262626',
          100: '#161616',
        },
        success: '#24a148',
        warning: '#f1c21b',
        error: '#da1e28',
        info: '#4589ff',
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #0f62fe 0%, #009d9a 100%)',
        'gradient-secondary': 'linear-gradient(135deg, #f4f4f4 0%, #ffffff 100%)',
        'gradient-dark': 'linear-gradient(135deg, #262626 0%, #161616 100%)',
      }
    },
  },
  plugins: [],
};
