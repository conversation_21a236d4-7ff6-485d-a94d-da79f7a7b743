import  { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>hn<PERSON>, Line } from 'react-chartjs-2';
import { 
  Chart as ChartJS, 
  CategoryScale, 
  LinearScale, 
  BarElement, 
  PointElement,
  LineElement,
  ArcElement,
  Title, 
  Tooltip, 
  Legend,
  Filler
} from 'chart.js';
import { units, equipment, getAIInsights } from '../data/mockData';

// Register ChartJS components
ChartJS.register(
  CategoryScale, 
  LinearScale, 
  BarElement, 
  PointElement,
  LineElement,
  ArcElement,
  Title, 
  Tooltip, 
  Legend,
  Filler
);

function BarChart2(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <line x1="18" y1="20" x2="18" y2="10" />
      <line x1="12" y1="20" x2="12" y2="4" />
      <line x1="6" y1="20" x2="6" y2="14" />
    </svg>
  );
}

function Users(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  );
}

function Package(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m16.5 9.4-9-5.19M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
      <polyline points="3.29 7 12 12 20.71 7" />
      <line x1="12" y1="22" x2="12" y2="12" />
    </svg>
  );
}

function AlertCircle(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <circle cx="12" cy="12" r="10" />
      <line x1="12" y1="8" x2="12" y2="12" />
      <line x1="12" y1="16" x2="12.01" y2="16" />
    </svg>
  );
}

function ArrowUp(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="m5 12 7-7 7 7" />
      <path d="M12 19V5" />
    </svg>
  );
}

function ArrowDown(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M12 5v14" />
      <path d="m19 12-7 7-7-7" />
    </svg>
  );
}

function RefreshCw(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
      <path d="M3 3v5h5" />
    </svg>
  );
}

function Target(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <circle cx="12" cy="12" r="10" />
      <circle cx="12" cy="12" r="6" />
      <circle cx="12" cy="12" r="2" />
    </svg>
  );
}

function Shield(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
    </svg>
  );
}

function Info(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <circle cx="12" cy="12" r="10" />
      <path d="M12 16v-4" />
      <path d="M12 8h.01" />
    </svg>
  );
}

export default function Dashboard() {
  const [unitsData, setUnitsData] = useState({
    friendly: { total: 0, active: 0, reserve: 0, damaged: 0 },
    enemy: { total: 0, active: 0, reserve: 0, damaged: 0 }
  });
  
  const [equipmentData, setEquipmentData] = useState({
    friendly: { total: 0, operational: 0, maintenance: 0, damaged: 0 },
    enemy: { total: 0, operational: 0, maintenance: 0, damaged: 0 }
  });
  
  const [readinessData, setReadinessData] = useState({
    friendly: 0,
    enemy: 0
  });
  
  const [aiInsights, setAiInsights] = useState<string[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  
  useEffect(() => {
    // Count units by side and status
    const unitStats = {
      friendly: { total: 0, active: 0, reserve: 0, damaged: 0 },
      enemy: { total: 0, active: 0, reserve: 0, damaged: 0 }
    };
    
    units.forEach(unit => {
      unitStats[unit.side].total++;
      unitStats[unit.side][unit.status]++;
    });
    
    setUnitsData(unitStats);
    
    // Count equipment by side and status
    const equipmentStats = {
      friendly: { total: 0, operational: 0, maintenance: 0, damaged: 0 },
      enemy: { total: 0, operational: 0, maintenance: 0, damaged: 0 }
    };
    
    equipment.forEach(eq => {
      if (eq.unitId) {
        const unit = units.find(u => u.id === eq.unitId);
        if (unit) {
          equipmentStats[unit.side].total++;
          equipmentStats[unit.side][eq.status]++;
        }
      }
    });
    
    setEquipmentData(equipmentStats);
    
    // Calculate average readiness
    const readinessStats = { friendly: 0, enemy: 0 };
    const friendlyUnits = units.filter(u => u.side === 'friendly');
    const enemyUnits = units.filter(u => u.side === 'enemy');
    
    if (friendlyUnits.length > 0) {
      readinessStats.friendly = Math.round(
        friendlyUnits.reduce((sum, u) => sum + u.readiness, 0) / friendlyUnits.length
      );
    }
    
    if (enemyUnits.length > 0) {
      readinessStats.enemy = Math.round(
        enemyUnits.reduce((sum, u) => sum + u.readiness, 0) / enemyUnits.length
      );
    }
    
    setReadinessData(readinessStats);
    
    // Get AI insights
    setAiInsights(getAIInsights());
  }, []);
  
  const refreshData = () => {
    setRefreshing(true);
    
    // Simulate data refresh
    setTimeout(() => {
      setAiInsights(getAIInsights());
      setRefreshing(false);
    }, 1000);
  };
  
  // Prepare chart data
  const getUnitsStatusData = () => {
    return {
      labels: ['نشط', 'احتياط', 'متضرر'],
      datasets: [
        {
          label: 'قواتنا',
          data: [unitsData.friendly.active, unitsData.friendly.reserve, unitsData.friendly.damaged],
          backgroundColor: 'rgba(59, 130, 246, 0.7)',
        },
        {
          label: 'العدو',
          data: [unitsData.enemy.active, unitsData.enemy.reserve, unitsData.enemy.damaged],
          backgroundColor: 'rgba(239, 68, 68, 0.7)',
        },
      ],
    };
  };
  
  const getEquipmentData = () => {
    return {
      labels: ['دبابات', 'مدفعية', 'دفاع جوي', 'مروحيات', 'أخرى'],
      datasets: [
        {
          label: 'قواتنا',
          data: [
            equipment.filter(e => e.type === 'tank' && units.find(u => u.id === e.unitId)?.side === 'friendly').length,
            equipment.filter(e => e.type === 'artillery' && units.find(u => u.id === e.unitId)?.side === 'friendly').length,
            equipment.filter(e => e.type === 'anti_air' && units.find(u => u.id === e.unitId)?.side === 'friendly').length,
            equipment.filter(e => e.type === 'helicopter' && units.find(u => u.id === e.unitId)?.side === 'friendly').length,
            equipment.filter(e => !['tank', 'artillery', 'anti_air', 'helicopter'].includes(e.type) && units.find(u => u.id === e.unitId)?.side === 'friendly').length,
          ],
          backgroundColor: 'rgba(59, 130, 246, 0.7)',
        },
        {
          label: 'العدو',
          data: [
            equipment.filter(e => e.type === 'tank' && units.find(u => u.id === e.unitId)?.side === 'enemy').length,
            equipment.filter(e => e.type === 'artillery' && units.find(u => u.id === e.unitId)?.side === 'enemy').length,
            equipment.filter(e => e.type === 'anti_air' && units.find(u => u.id === e.unitId)?.side === 'enemy').length,
            equipment.filter(e => e.type === 'helicopter' && units.find(u => u.id === e.unitId)?.side === 'enemy').length,
            equipment.filter(e => !['tank', 'artillery', 'anti_air', 'helicopter'].includes(e.type) && units.find(u => u.id === e.unitId)?.side === 'enemy').length,
          ],
          backgroundColor: 'rgba(239, 68, 68, 0.7)',
        },
      ],
    };
  };
  
  const getReadinessData = () => {
    return {
      labels: ['قواتنا', 'العدو'],
      datasets: [
        {
          data: [readinessData.friendly, readinessData.enemy],
          backgroundColor: [
            'rgba(59, 130, 246, 0.7)',
            'rgba(239, 68, 68, 0.7)',
          ],
          borderColor: [
            'rgba(59, 130, 246, 1)',
            'rgba(239, 68, 68, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
  };
  
  const getOperationalTrendData = () => {
    // This would ideally use historical data, but we'll mock it for this example
    return {
      labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
      datasets: [
        {
          label: 'جاهزية قواتنا',
          data: [65, 68, 72, 75, 82, readinessData.friendly],
          borderColor: 'rgba(59, 130, 246, 1)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
          fill: true,
        },
        {
          label: 'جاهزية العدو',
          data: [80, 75, 70, 73, 77, readinessData.enemy],
          borderColor: 'rgba(239, 68, 68, 1)',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          tension: 0.4,
          fill: true,
        },
      ],
    };
  };
  
  return (
    <div className="h-full overflow-auto p-6 bg-gradient-secondary">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-3xl font-bold font-heliopolis text-neutral-90 bg-gradient-primary bg-clip-text text-transparent">
          لوحة المعلومات
        </h1>
        <button
          onClick={refreshData}
          className="flex items-center rounded-xl bg-gradient-primary px-6 py-3 text-white font-heliopolis font-medium shadow-lg transition-all duration-200 hover:shadow-xl hover:scale-105"
          disabled={refreshing}
        >
          <RefreshCw size={20} className={`ml-2 ${refreshing ? 'animate-spin' : ''}`} />
          <span>تحديث البيانات</span>
        </button>
      </div>
      
      {/* Summary Cards */}
      <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-2xl border border-neutral-20 bg-white/80 p-6 shadow-xl backdrop-blur-sm transition-all duration-200 hover:shadow-2xl hover:scale-105">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="font-medium font-heliopolis text-neutral-80">إجمالي القوات</h2>
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-primary-500 to-secondary-500 shadow-lg">
              <Users size={24} className="text-white" />
            </div>
          </div>
          <div className="mb-3 text-4xl font-bold font-heliopolis text-neutral-90">
            {unitsData.friendly.total + unitsData.enemy.total}
          </div>
          <div className="flex items-center text-sm font-heliopolis">
            <div className="flex items-center text-success">
              <ArrowUp size={16} className="ml-1" />
              <span className="font-medium">5%</span>
            </div>
            <span className="mr-2 text-neutral-60">مقارنة بالشهر الماضي</span>
          </div>
        </div>

        <div className="rounded-2xl border border-neutral-20 bg-white/80 p-6 shadow-xl backdrop-blur-sm transition-all duration-200 hover:shadow-2xl hover:scale-105">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="font-medium font-heliopolis text-neutral-80">العتاد العسكري</h2>
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-success to-secondary-500 shadow-lg">
              <Package size={24} className="text-white" />
            </div>
          </div>
          <div className="mb-3 text-4xl font-bold font-heliopolis text-neutral-90">
            {equipmentData.friendly.total + equipmentData.enemy.total}
          </div>
          <div className="flex items-center text-sm font-heliopolis">
            <div className="flex items-center text-error">
              <ArrowDown size={16} className="ml-1" />
              <span className="font-medium">2%</span>
            </div>
            <span className="mr-2 text-neutral-60">مقارنة بالشهر الماضي</span>
          </div>
        </div>

        <div className="rounded-2xl border border-neutral-20 bg-white/80 p-6 shadow-xl backdrop-blur-sm transition-all duration-200 hover:shadow-2xl hover:scale-105">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="font-medium font-heliopolis text-neutral-80">جاهزية قواتنا</h2>
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-primary-500 to-info shadow-lg">
              <Shield size={24} className="text-white" />
            </div>
          </div>
          <div className="mb-3 text-4xl font-bold font-heliopolis text-neutral-90">
            {readinessData.friendly}%
          </div>
          <div className="flex items-center text-sm font-heliopolis">
            <div className="flex items-center text-success">
              <ArrowUp size={16} className="ml-1" />
              <span className="font-medium">8%</span>
            </div>
            <span className="mr-2 text-neutral-60">مقارنة بالشهر الماضي</span>
          </div>
        </div>

        <div className="rounded-2xl border border-neutral-20 bg-white/80 p-6 shadow-xl backdrop-blur-sm transition-all duration-200 hover:shadow-2xl hover:scale-105">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="font-medium font-heliopolis text-neutral-80">تهديدات محتملة</h2>
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-error to-warning shadow-lg">
              <Target size={24} className="text-white" />
            </div>
          </div>
          <div className="mb-3 text-4xl font-bold font-heliopolis text-neutral-90">
            {unitsData.enemy.active}
          </div>
          <div className="flex items-center text-sm font-heliopolis">
            <div className="flex items-center text-warning">
              <RefreshCw size={16} className="ml-1" />
              <span className="font-medium">ثابت</span>
            </div>
            <span className="mr-2 text-neutral-60">مقارنة بالشهر الماضي</span>
          </div>
        </div>
      </div>
      
      {/* Charts */}
      <div className="mb-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="rounded-2xl border border-neutral-20 bg-white/80 p-6 shadow-xl backdrop-blur-sm">
          <h2 className="mb-6 text-xl font-bold font-heliopolis text-neutral-90">حالة القوات</h2>
          <div style={{ height: '300px' }}>
            <Bar
              data={getUnitsStatusData()}
              options={{
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true
                  }
                }
              }}
            />
          </div>
        </div>

        <div className="rounded-2xl border border-neutral-20 bg-white/80 p-6 shadow-xl backdrop-blur-sm">
          <h2 className="mb-6 text-xl font-bold font-heliopolis text-neutral-90">توزيع العتاد</h2>
          <div style={{ height: '300px' }}>
            <Bar
              data={getEquipmentData()}
              options={{
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true
                  }
                }
              }}
            />
          </div>
        </div>
      </div>
      
      <div className="mb-8 grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div className="rounded-2xl border border-neutral-20 bg-white/80 p-6 shadow-xl backdrop-blur-sm lg:col-span-2">
          <h2 className="mb-6 text-xl font-bold font-heliopolis text-neutral-90">تحليل الجاهزية عبر الزمن</h2>
          <div style={{ height: '300px' }}>
            <Line
              data={getOperationalTrendData()}
              options={{
                maintainAspectRatio: false,
                scales: {
                  y: {
                    min: 0,
                    max: 100
                  }
                }
              }}
            />
          </div>
        </div>

        <div className="rounded-2xl border border-neutral-20 bg-white/80 p-6 shadow-xl backdrop-blur-sm">
          <h2 className="mb-6 text-xl font-bold font-heliopolis text-neutral-90">مقارنة الجاهزية</h2>
          <div style={{ height: '300px' }}>
            <Doughnut
              data={getReadinessData()}
              options={{
                maintainAspectRatio: false,
              }}
            />
          </div>
        </div>
      </div>
      
      {/* AI Insights */}
      <div className="rounded-2xl border border-neutral-20 bg-white/80 p-6 shadow-xl backdrop-blur-sm">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-xl font-bold font-heliopolis text-neutral-90">تحليلات الذكاء الاصطناعي</h2>
          <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-primary shadow-lg">
            <BarChart2 size={24} className="text-white" />
          </div>
        </div>

        <div className="space-y-4">
          {aiInsights.map((insight, idx) => (
            <div key={idx} className="flex items-start rounded-xl border border-primary-200 bg-gradient-to-r from-primary-50 to-secondary-50 p-4 transition-all duration-200 hover:shadow-lg">
              <Info size={24} className="mt-1 text-primary-600" />
              <div className="mr-4">
                <p className="font-heliopolis text-neutral-90 font-medium">{insight}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
 