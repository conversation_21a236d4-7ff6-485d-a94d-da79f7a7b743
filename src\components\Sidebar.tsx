import  { useState } from 'react';
import { Layout, Map, Users, Package, BarChart2, Target, Settings, Sliders } from 'lucide-react';

interface SidebarProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export default function Sidebar({ activeTab, setActiveTab }: SidebarProps) {
  const [collapsed, setCollapsed] = useState(false);

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <div
      className={`bg-gradient-dark text-white transition-all duration-300 ease-in-out shadow-2xl ${
        collapsed ? 'w-16' : 'w-64'
      }`}
    >
      <div className="flex h-16 items-center justify-between border-b border-neutral-70 px-4 bg-gradient-primary">
        <h2 className={`text-xl font-bold font-heliopolis ${collapsed ? 'hidden' : 'block'}`}>
          نظام العمليات
        </h2>
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="rounded-lg p-2 hover:bg-white/10 transition-colors duration-200"
        >
          <Layout size={20} />
        </button>
      </div>

      <nav className="mt-8 space-y-1 px-3">
        <button
          onClick={() => handleTabClick('dashboard')}
          className={`flex w-full items-center rounded-xl p-3 font-heliopolis transition-all duration-200 ${
            activeTab === 'dashboard'
              ? 'bg-gradient-primary text-white shadow-lg transform scale-105'
              : 'hover:bg-white/10 hover:transform hover:scale-105'
          }`}
        >
          <BarChart2 size={22} />
          <span className={`mr-3 font-medium ${collapsed ? 'hidden' : 'block'}`}>
            لوحة المعلومات
          </span>
        </button>

        <button
          onClick={() => handleTabClick('map')}
          className={`flex w-full items-center rounded-xl p-3 font-heliopolis transition-all duration-200 ${
            activeTab === 'map'
              ? 'bg-gradient-primary text-white shadow-lg transform scale-105'
              : 'hover:bg-white/10 hover:transform hover:scale-105'
          }`}
        >
          <Map size={22} />
          <span className={`mr-3 font-medium ${collapsed ? 'hidden' : 'block'}`}>
            الخريطة الميدانية
          </span>
        </button>

        <button
          onClick={() => handleTabClick('friendly')}
          className={`flex w-full items-center rounded-xl p-3 font-heliopolis transition-all duration-200 ${
            activeTab === 'friendly'
              ? 'bg-gradient-primary text-white shadow-lg transform scale-105'
              : 'hover:bg-white/10 hover:transform hover:scale-105'
          }`}
        >
          <Users size={22} />
          <span className={`mr-3 font-medium ${collapsed ? 'hidden' : 'block'}`}>
            القوات الصديقة
          </span>
        </button>

        <button
          onClick={() => handleTabClick('enemy')}
          className={`flex w-full items-center rounded-xl p-3 font-heliopolis transition-all duration-200 ${
            activeTab === 'enemy'
              ? 'bg-gradient-primary text-white shadow-lg transform scale-105'
              : 'hover:bg-white/10 hover:transform hover:scale-105'
          }`}
        >
          <Target size={22} />
          <span className={`mr-3 font-medium ${collapsed ? 'hidden' : 'block'}`}>
            قوات العدو
          </span>
        </button>

        <button
          onClick={() => handleTabClick('equipment')}
          className={`flex w-full items-center rounded-xl p-3 font-heliopolis transition-all duration-200 ${
            activeTab === 'equipment'
              ? 'bg-gradient-primary text-white shadow-lg transform scale-105'
              : 'hover:bg-white/10 hover:transform hover:scale-105'
          }`}
        >
          <Package size={22} />
          <span className={`mr-3 font-medium ${collapsed ? 'hidden' : 'block'}`}>
            العتاد العسكري
          </span>
        </button>

        <button
          onClick={() => handleTabClick('analysis')}
          className={`flex w-full items-center rounded-xl p-3 font-heliopolis transition-all duration-200 ${
            activeTab === 'analysis'
              ? 'bg-gradient-primary text-white shadow-lg transform scale-105'
              : 'hover:bg-white/10 hover:transform hover:scale-105'
          }`}
        >
          <Sliders size={22} />
          <span className={`mr-3 font-medium ${collapsed ? 'hidden' : 'block'}`}>
            تحليل الذكاء الاصطناعي
          </span>
        </button>

        <button
          onClick={() => handleTabClick('battleplanner')}
          className={`flex w-full items-center rounded-xl p-3 font-heliopolis transition-all duration-200 ${
            activeTab === 'battleplanner'
              ? 'bg-gradient-primary text-white shadow-lg transform scale-105'
              : 'hover:bg-white/10 hover:transform hover:scale-105'
          }`}
        >
          <Sliders size={22} />
          <span className={`mr-3 font-medium ${collapsed ? 'hidden' : 'block'}`}>
            تخطيط المعارك
          </span>
        </button>

        <button
          onClick={() => handleTabClick('settings')}
          className={`flex w-full items-center rounded-xl p-3 font-heliopolis transition-all duration-200 ${
            activeTab === 'settings'
              ? 'bg-gradient-primary text-white shadow-lg transform scale-105'
              : 'hover:bg-white/10 hover:transform hover:scale-105'
          }`}
        >
          <Settings size={22} />
          <span className={`mr-3 font-medium ${collapsed ? 'hidden' : 'block'}`}>
            الإعدادات
          </span>
        </button>
      </nav>

      <div className={`absolute bottom-0 w-full p-4 ${collapsed ? 'hidden' : 'block'}`}>
        <div className="text-xs text-neutral-30 font-heliopolis bg-neutral-90/50 rounded-lg p-3 backdrop-blur-sm">
          <p className="font-medium">إصدار النظام: 2.0.0</p>
          <p>آخر تحديث: 2025/06/29</p>
        </div>
      </div>
    </div>
  );
}
 