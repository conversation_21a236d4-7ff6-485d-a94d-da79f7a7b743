import React, { useState, useEffect } from 'react';
import { Unit, Equipment } from '../types';
import { units, equipment } from '../data/mockData';

interface MapBattleSimulationProps {
  selectedFriendlyUnits: string[];
  selectedEnemyUnits: string[];
  onBattleComplete: (result: {
    winner: 'friendly' | 'enemy' | 'draw';
    friendlyLosses: number;
    enemyLosses: number;
    duration: number;
    summary: string;
  }) => void;
  targetingData?: {
    targetUnitId: string;
    attackerUnitId: string;
    weaponType: string;
    accuracy: number;
  };
}

interface BattleUnit {
  id: string;
  name: string;
  side: 'friendly' | 'enemy';
  position: [number, number];
  health: number;
  maxHealth: number;
  firepower: number;
  accuracy: number;
  range: number;
  armor: number;
  status: 'active' | 'damaged' | 'destroyed';
  lastFired: number;
  ammunition: number;
  maxAmmunition: number;
}

interface BattleEffect {
  id: string;
  type: 'shot' | 'explosion' | 'hit';
  position: [number, number];
  timestamp: number;
  source?: string;
  target?: string;
}

export default function MapBattleSimulation({ 
  selectedFriendlyUnits, 
  selectedEnemyUnits, 
  onBattleComplete,
  targetingData 
}: MapBattleSimulationProps) {
  const [battleUnits, setBattleUnits] = useState<BattleUnit[]>([]);
  const [battleEffects, setBattleEffects] = useState<BattleEffect[]>([]);
  const [battleTime, setBattleTime] = useState(0);
  const [isActive, setIsActive] = useState(false);

  // تحويل الوحدات المختارة إلى وحدات معركة
  useEffect(() => {
    const initializeBattleUnits = () => {
      const friendlyBattleUnits: BattleUnit[] = selectedFriendlyUnits.map(unitId => {
        const unit = units.find(u => u.id === unitId);
        if (!unit) return null;
        
        const unitEquipment = equipment.filter(eq => eq.unitId === unitId);
        const firepower = unitEquipment.reduce((sum, eq) => {
          switch(eq.type) {
            case 'tank': return sum + 80;
            case 'artillery': return sum + 100;
            case 'anti_air': return sum + 60;
            case 'helicopter': return sum + 90;
            default: return sum + 30;
          }
        }, unit.personnelCount * 2);

        return {
          id: unit.id,
          name: unit.name,
          side: 'friendly' as const,
          position: [unit.location.coordinates[0][0][0], unit.location.coordinates[0][0][1]],
          health: 100,
          maxHealth: 100,
          firepower,
          accuracy: unit.readiness,
          range: 5000,
          armor: unitEquipment.filter(eq => eq.type === 'tank').length * 20,
          status: 'active' as const,
          lastFired: 0,
          ammunition: 100,
          maxAmmunition: 100
        };
      }).filter(Boolean) as BattleUnit[];

      const enemyBattleUnits: BattleUnit[] = selectedEnemyUnits.map(unitId => {
        const unit = units.find(u => u.id === unitId);
        if (!unit) return null;
        
        const unitEquipment = equipment.filter(eq => eq.unitId === unitId);
        const firepower = unitEquipment.reduce((sum, eq) => {
          switch(eq.type) {
            case 'tank': return sum + 75;
            case 'artillery': return sum + 95;
            case 'anti_air': return sum + 55;
            case 'helicopter': return sum + 85;
            default: return sum + 25;
          }
        }, unit.personnelCount * 1.8);

        return {
          id: unit.id,
          name: unit.name,
          side: 'enemy' as const,
          position: [unit.location.coordinates[0][0][0], unit.location.coordinates[0][0][1]],
          health: 100,
          maxHealth: 100,
          firepower,
          accuracy: unit.readiness * 0.9,
          range: 4500,
          armor: unitEquipment.filter(eq => eq.type === 'tank').length * 18,
          status: 'active' as const,
          lastFired: 0,
          ammunition: 90,
          maxAmmunition: 90
        };
      }).filter(Boolean) as BattleUnit[];

      setBattleUnits([...friendlyBattleUnits, ...enemyBattleUnits]);
    };

    initializeBattleUnits();
  }, [selectedFriendlyUnits, selectedEnemyUnits]);

  // بدء المحاكاة
  const startBattle = () => {
    setIsActive(true);
    setBattleTime(0);
    setBattleEffects([]);
  };

  // حساب المسافة بين وحدتين
  const calculateDistance = (pos1: [number, number], pos2: [number, number]): number => {
    const R = 6371000;
    const lat1 = pos1[0] * Math.PI / 180;
    const lat2 = pos2[0] * Math.PI / 180;
    const deltaLat = (pos2[0] - pos1[0]) * Math.PI / 180;
    const deltaLng = (pos2[1] - pos1[1]) * Math.PI / 180;

    const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(deltaLng/2) * Math.sin(deltaLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  };

  // تحديد أفضل هدف للوحدة
  const findBestTarget = (attacker: BattleUnit, enemies: BattleUnit[]): BattleUnit | null => {
    const validTargets = enemies.filter(enemy => {
      const distance = calculateDistance(attacker.position, enemy.position);
      return enemy.status === 'active' && distance <= attacker.range;
    });

    if (validTargets.length === 0) return null;

    // إذا كان لدينا بيانات استهداف محددة، استخدمها
    if (targetingData && attacker.id === targetingData.attackerUnitId) {
      const specificTarget = validTargets.find(t => t.id === targetingData.targetUnitId);
      if (specificTarget) return specificTarget;
    }

    // اختيار الهدف الأقرب والأضعف
    return validTargets.reduce((best, current) => {
      const bestDistance = calculateDistance(attacker.position, best.position);
      const currentDistance = calculateDistance(attacker.position, current.position);
      const bestScore = (100 - best.health) / bestDistance;
      const currentScore = (100 - current.health) / currentDistance;
      return currentScore > bestScore ? current : best;
    });
  };

  // تنفيذ هجوم
  const executeAttack = (attacker: BattleUnit, target: BattleUnit) => {
    const distance = calculateDistance(attacker.position, target.position);
    
    // حساب دقة الإصابة
    let hitChance = attacker.accuracy / 100;
    
    // تطبيق بيانات الاستهداف إذا كانت متوفرة
    if (targetingData && attacker.id === targetingData.attackerUnitId && target.id === targetingData.targetUnitId) {
      hitChance = targetingData.accuracy / 100;
    }
    
    // تقليل الدقة بناءً على المسافة
    hitChance *= Math.max(0.3, 1 - (distance / attacker.range));
    
    // إضافة تأثير إطلاق النار
    setBattleEffects(prev => [...prev, {
      id: `shot_${Date.now()}_${Math.random()}`,
      type: 'shot',
      position: attacker.position,
      timestamp: Date.now()
    }]);

    if (Math.random() < hitChance) {
      // إصابة
      const baseDamage = attacker.firepower / 10;
      const finalDamage = Math.max(1, baseDamage - target.armor / 10);
      
      // تحديث صحة الهدف
      const newHealth = Math.max(0, target.health - finalDamage);
      target.health = newHealth;

      // إضافة تأثير الانفجار
      setBattleEffects(prev => [...prev, {
        id: `explosion_${Date.now()}_${Math.random()}`,
        type: 'explosion',
        position: target.position,
        timestamp: Date.now(),
        source: attacker.id,
        target: target.id
      }]);

      if (newHealth <= 0) {
        target.status = 'destroyed';
      } else if (newHealth <= 30) {
        target.status = 'damaged';
      }
    }

    // تقليل الذخيرة
    attacker.ammunition = Math.max(0, attacker.ammunition - 1);
    attacker.lastFired = battleTime;
  };

  // محاكاة المعركة
  useEffect(() => {
    if (!isActive) return;

    const interval = setInterval(() => {
      setBattleTime(prev => prev + 1);

      const friendlyUnits = battleUnits.filter(u => u.side === 'friendly' && u.status === 'active');
      const enemyUnits = battleUnits.filter(u => u.side === 'enemy' && u.status === 'active');

      if (friendlyUnits.length === 0 || enemyUnits.length === 0) {
        // انتهاء المعركة
        const winner = friendlyUnits.length > 0 ? 'friendly' : enemyUnits.length > 0 ? 'enemy' : 'draw';
        const totalFriendly = battleUnits.filter(u => u.side === 'friendly').length;
        const totalEnemy = battleUnits.filter(u => u.side === 'enemy').length;
        const friendlyLosses = ((totalFriendly - friendlyUnits.length) / totalFriendly) * 100;
        const enemyLosses = ((totalEnemy - enemyUnits.length) / totalEnemy) * 100;

        onBattleComplete({
          winner,
          friendlyLosses: Math.round(friendlyLosses),
          enemyLosses: Math.round(enemyLosses),
          duration: battleTime,
          summary: winner === 'friendly' ? 'انتصار ساحق للقوات الصديقة!' : 
                   winner === 'enemy' ? 'هزيمة مؤلمة أمام قوات العدو' : 'معركة متعادلة'
        });
        setIsActive(false);
        return;
      }

      // تنفيذ الهجمات
      [...friendlyUnits, ...enemyUnits].forEach(unit => {
        if (unit.ammunition > 0 && battleTime - unit.lastFired >= 3) {
          const enemies = unit.side === 'friendly' ? enemyUnits : friendlyUnits;
          const target = findBestTarget(unit, enemies);
          
          if (target) {
            executeAttack(unit, target);
          }
        }
      });

      // إنهاء المعركة بعد 5 دقائق كحد أقصى
      if (battleTime >= 300) {
        const friendlyAlive = friendlyUnits.length;
        const enemyAlive = enemyUnits.length;
        const winner = friendlyAlive > enemyAlive ? 'friendly' : enemyAlive > friendlyAlive ? 'enemy' : 'draw';
        
        onBattleComplete({
          winner,
          friendlyLosses: Math.round(((battleUnits.filter(u => u.side === 'friendly').length - friendlyAlive) / battleUnits.filter(u => u.side === 'friendly').length) * 100),
          enemyLosses: Math.round(((battleUnits.filter(u => u.side === 'enemy').length - enemyAlive) / battleUnits.filter(u => u.side === 'enemy').length) * 100),
          duration: battleTime,
          summary: 'انتهت المعركة بسبب انتهاء الوقت المحدد'
        });
        setIsActive(false);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isActive, battleTime, battleUnits, onBattleComplete]);

  // تنظيف التأثيرات القديمة
  useEffect(() => {
    const cleanup = setInterval(() => {
      setBattleEffects(prev => prev.filter(effect => Date.now() - effect.timestamp < 3000));
    }, 1000);

    return () => clearInterval(cleanup);
  }, []);

  // بدء المحاكاة تلقائياً
  useEffect(() => {
    if (battleUnits.length > 0 && !isActive) {
      startBattle();
    }
  }, [battleUnits]);

  // هذا المكون لا يعرض شيئاً مرئياً، فقط يدير المحاكاة
  return null;
}
