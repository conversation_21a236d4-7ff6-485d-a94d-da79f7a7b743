import React, { useState, useEffect } from 'react';
import { Unit, Equipment } from '../types';
import { units, equipment } from '../data/mockData';

interface AdvancedBattleSimulationProps {
  onClose: () => void;
  selectedFriendlyUnits: string[];
  selectedEnemyUnits: string[];
  targetingData?: {
    targetUnitId: string;
    attackerUnitId: string;
    weaponType: string;
    accuracy: number;
  };
  onMapBattle?: boolean; // للتحكم في عرض المحاكاة على الخريطة أو في نافذة منفصلة
}

interface BattleUnit {
  id: string;
  name: string;
  side: 'friendly' | 'enemy';
  position: [number, number];
  health: number;
  maxHealth: number;
  firepower: number;
  accuracy: number;
  range: number;
  armor: number;
  status: 'active' | 'damaged' | 'destroyed';
  lastFired: number;
  ammunition: number;
  maxAmmunition: number;
}

interface BattleEvent {
  id: string;
  time: number;
  type: 'shot' | 'hit' | 'miss' | 'destroyed' | 'retreat';
  source: string;
  target?: string;
  damage?: number;
  message: string;
}

export default function AdvancedBattleSimulation({ 
  onClose, 
  selectedFriendlyUnits, 
  selectedEnemyUnits,
  targetingData 
}: AdvancedBattleSimulationProps) {
  const [battleUnits, setBattleUnits] = useState<BattleUnit[]>([]);
  const [battleEvents, setBattleEvents] = useState<BattleEvent[]>([]);
  const [battleTime, setBattleTime] = useState(0);
  const [phase, setPhase] = useState<'preparation' | 'battle' | 'result'>('preparation');
  const [battleResult, setBattleResult] = useState<{
    winner: 'friendly' | 'enemy' | 'draw';
    friendlyLosses: number;
    enemyLosses: number;
    duration: number;
    summary: string;
  } | null>(null);

  // تحويل الوحدات المختارة إلى وحدات معركة
  useEffect(() => {
    const initializeBattleUnits = () => {
      const friendlyBattleUnits: BattleUnit[] = selectedFriendlyUnits.map(unitId => {
        const unit = units.find(u => u.id === unitId);
        if (!unit) return null;
        
        const unitEquipment = equipment.filter(eq => eq.unitId === unitId);
        const firepower = unitEquipment.reduce((sum, eq) => {
          switch(eq.type) {
            case 'tank': return sum + 80;
            case 'artillery': return sum + 100;
            case 'anti_air': return sum + 60;
            case 'helicopter': return sum + 90;
            default: return sum + 30;
          }
        }, unit.personnelCount * 2);

        return {
          id: unit.id,
          name: unit.name,
          side: 'friendly' as const,
          position: [unit.location.coordinates[0][0][0], unit.location.coordinates[0][0][1]],
          health: 100,
          maxHealth: 100,
          firepower,
          accuracy: unit.readiness,
          range: 5000, // متر
          armor: unitEquipment.filter(eq => eq.type === 'tank').length * 20,
          status: 'active' as const,
          lastFired: 0,
          ammunition: 100,
          maxAmmunition: 100
        };
      }).filter(Boolean) as BattleUnit[];

      const enemyBattleUnits: BattleUnit[] = selectedEnemyUnits.map(unitId => {
        const unit = units.find(u => u.id === unitId);
        if (!unit) return null;
        
        const unitEquipment = equipment.filter(eq => eq.unitId === unitId);
        const firepower = unitEquipment.reduce((sum, eq) => {
          switch(eq.type) {
            case 'tank': return sum + 75;
            case 'artillery': return sum + 95;
            case 'anti_air': return sum + 55;
            case 'helicopter': return sum + 85;
            default: return sum + 25;
          }
        }, unit.personnelCount * 1.8);

        return {
          id: unit.id,
          name: unit.name,
          side: 'enemy' as const,
          position: [unit.location.coordinates[0][0][0], unit.location.coordinates[0][0][1]],
          health: 100,
          maxHealth: 100,
          firepower,
          accuracy: unit.readiness * 0.9, // العدو أقل دقة قليلاً
          range: 4500,
          armor: unitEquipment.filter(eq => eq.type === 'tank').length * 18,
          status: 'active' as const,
          lastFired: 0,
          ammunition: 90,
          maxAmmunition: 90
        };
      }).filter(Boolean) as BattleUnit[];

      setBattleUnits([...friendlyBattleUnits, ...enemyBattleUnits]);
    };

    initializeBattleUnits();
  }, [selectedFriendlyUnits, selectedEnemyUnits]);

  // حساب المسافة بين وحدتين
  const calculateDistance = (pos1: [number, number], pos2: [number, number]): number => {
    const R = 6371000; // نصف قطر الأرض بالمتر
    const lat1 = pos1[0] * Math.PI / 180;
    const lat2 = pos2[0] * Math.PI / 180;
    const deltaLat = (pos2[0] - pos1[0]) * Math.PI / 180;
    const deltaLng = (pos2[1] - pos1[1]) * Math.PI / 180;

    const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(deltaLng/2) * Math.sin(deltaLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  };

  // تحديد أفضل هدف للوحدة
  const findBestTarget = (attacker: BattleUnit, enemies: BattleUnit[]): BattleUnit | null => {
    const validTargets = enemies.filter(enemy => {
      const distance = calculateDistance(attacker.position, enemy.position);
      return enemy.status === 'active' && distance <= attacker.range;
    });

    if (validTargets.length === 0) return null;

    // إذا كان لدينا بيانات استهداف محددة، استخدمها
    if (targetingData && attacker.id === targetingData.attackerUnitId) {
      const specificTarget = validTargets.find(t => t.id === targetingData.targetUnitId);
      if (specificTarget) return specificTarget;
    }

    // اختيار الهدف الأقرب والأضعف
    return validTargets.reduce((best, current) => {
      const bestDistance = calculateDistance(attacker.position, best.position);
      const currentDistance = calculateDistance(attacker.position, current.position);
      const bestScore = (100 - best.health) / bestDistance;
      const currentScore = (100 - current.health) / currentDistance;
      return currentScore > bestScore ? current : best;
    });
  };

  // تنفيذ هجوم
  const executeAttack = (attacker: BattleUnit, target: BattleUnit): BattleEvent[] => {
    const events: BattleEvent[] = [];
    const distance = calculateDistance(attacker.position, target.position);
    
    // حساب دقة الإصابة
    let hitChance = attacker.accuracy / 100;
    
    // تطبيق بيانات الاستهداف إذا كانت متوفرة
    if (targetingData && attacker.id === targetingData.attackerUnitId && target.id === targetingData.targetUnitId) {
      hitChance = targetingData.accuracy / 100;
    }
    
    // تقليل الدقة بناءً على المسافة
    hitChance *= Math.max(0.3, 1 - (distance / attacker.range));
    
    // إطلاق النار
    events.push({
      id: `shot_${Date.now()}_${Math.random()}`,
      time: battleTime,
      type: 'shot',
      source: attacker.id,
      target: target.id,
      message: `${attacker.name} يطلق النار على ${target.name}`
    });

    if (Math.random() < hitChance) {
      // إصابة
      const baseDamage = attacker.firepower / 10;
      const finalDamage = Math.max(1, baseDamage - target.armor / 10);
      
      events.push({
        id: `hit_${Date.now()}_${Math.random()}`,
        time: battleTime,
        type: 'hit',
        source: attacker.id,
        target: target.id,
        damage: finalDamage,
        message: `إصابة مباشرة! ${target.name} تلقت ${Math.round(finalDamage)} نقطة ضرر`
      });

      // تحديث صحة الهدف
      const newHealth = Math.max(0, target.health - finalDamage);
      target.health = newHealth;

      if (newHealth <= 0) {
        target.status = 'destroyed';
        events.push({
          id: `destroyed_${Date.now()}_${Math.random()}`,
          time: battleTime,
          type: 'destroyed',
          source: attacker.id,
          target: target.id,
          message: `${target.name} تم تدميرها بالكامل!`
        });
      } else if (newHealth <= 30) {
        target.status = 'damaged';
      }
    } else {
      // إخفاق
      events.push({
        id: `miss_${Date.now()}_${Math.random()}`,
        time: battleTime,
        type: 'miss',
        source: attacker.id,
        target: target.id,
        message: `${attacker.name} أخفقت في إصابة ${target.name}`
      });
    }

    // تقليل الذخيرة
    attacker.ammunition = Math.max(0, attacker.ammunition - 1);
    attacker.lastFired = battleTime;

    return events;
  };

  // بدء المعركة
  const startBattle = () => {
    setPhase('battle');
    setBattleTime(0);
    setBattleEvents([]);
  };

  // محاكاة المعركة
  useEffect(() => {
    if (phase !== 'battle') return;

    const interval = setInterval(() => {
      setBattleTime(prev => prev + 1);

      const friendlyUnits = battleUnits.filter(u => u.side === 'friendly' && u.status === 'active');
      const enemyUnits = battleUnits.filter(u => u.side === 'enemy' && u.status === 'active');

      if (friendlyUnits.length === 0 || enemyUnits.length === 0) {
        // انتهاء المعركة
        const winner = friendlyUnits.length > 0 ? 'friendly' : enemyUnits.length > 0 ? 'enemy' : 'draw';
        const totalFriendly = battleUnits.filter(u => u.side === 'friendly').length;
        const totalEnemy = battleUnits.filter(u => u.side === 'enemy').length;
        const friendlyLosses = ((totalFriendly - friendlyUnits.length) / totalFriendly) * 100;
        const enemyLosses = ((totalEnemy - enemyUnits.length) / totalEnemy) * 100;

        setBattleResult({
          winner,
          friendlyLosses: Math.round(friendlyLosses),
          enemyLosses: Math.round(enemyLosses),
          duration: battleTime,
          summary: winner === 'friendly' ? 'انتصار ساحق للقوات الصديقة!' : 
                   winner === 'enemy' ? 'هزيمة مؤلمة أمام قوات العدو' : 'معركة متعادلة'
        });
        setPhase('result');
        return;
      }

      // تنفيذ الهجمات
      const newEvents: BattleEvent[] = [];
      
      [...friendlyUnits, ...enemyUnits].forEach(unit => {
        if (unit.ammunition > 0 && battleTime - unit.lastFired >= 3) { // إطلاق كل 3 ثوان
          const enemies = unit.side === 'friendly' ? enemyUnits : friendlyUnits;
          const target = findBestTarget(unit, enemies);
          
          if (target) {
            const attackEvents = executeAttack(unit, target);
            newEvents.push(...attackEvents);
          }
        }
      });

      if (newEvents.length > 0) {
        setBattleEvents(prev => [...prev, ...newEvents].slice(-50)); // الاحتفاظ بآخر 50 حدث
      }

      // إنهاء المعركة بعد 5 دقائق كحد أقصى
      if (battleTime >= 300) {
        const friendlyAlive = friendlyUnits.length;
        const enemyAlive = enemyUnits.length;
        const winner = friendlyAlive > enemyAlive ? 'friendly' : enemyAlive > friendlyAlive ? 'enemy' : 'draw';
        
        setBattleResult({
          winner,
          friendlyLosses: Math.round(((battleUnits.filter(u => u.side === 'friendly').length - friendlyAlive) / battleUnits.filter(u => u.side === 'friendly').length) * 100),
          enemyLosses: Math.round(((battleUnits.filter(u => u.side === 'enemy').length - enemyAlive) / battleUnits.filter(u => u.side === 'enemy').length) * 100),
          duration: battleTime,
          summary: 'انتهت المعركة بسبب انتهاء الوقت المحدد'
        });
        setPhase('result');
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [phase, battleTime, battleUnits]);

  return (
    <div className="fixed inset-0 z-[10000] bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        <div className="bg-gradient-primary text-white p-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold font-heliopolis">محاكاة معركة حقيقية متقدمة</h2>
            <button
              onClick={onClose}
              className="rounded-xl bg-white/20 p-2 hover:bg-white/30 transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {phase === 'preparation' && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-xl font-bold font-heliopolis text-neutral-90 mb-4">
                  إعداد المعركة
                </h3>
                <p className="text-neutral-70 font-heliopolis">
                  تم اختيار {selectedFriendlyUnits.length} وحدة صديقة و {selectedEnemyUnits.length} وحدة معادية
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gradient-to-r from-success/10 to-success/5 rounded-xl p-4">
                  <h4 className="font-bold text-success mb-3 font-heliopolis">القوات الصديقة</h4>
                  <div className="space-y-2">
                    {battleUnits.filter(u => u.side === 'friendly').map(unit => (
                      <div key={unit.id} className="flex justify-between items-center bg-white/50 rounded-lg p-2">
                        <span className="font-heliopolis">{unit.name}</span>
                        <span className="text-sm text-neutral-60">قوة: {unit.firepower}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-gradient-to-r from-error/10 to-error/5 rounded-xl p-4">
                  <h4 className="font-bold text-error mb-3 font-heliopolis">القوات المعادية</h4>
                  <div className="space-y-2">
                    {battleUnits.filter(u => u.side === 'enemy').map(unit => (
                      <div key={unit.id} className="flex justify-between items-center bg-white/50 rounded-lg p-2">
                        <span className="font-heliopolis">{unit.name}</span>
                        <span className="text-sm text-neutral-60">قوة: {unit.firepower}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {targetingData && (
                <div className="bg-gradient-to-r from-info/10 to-info/5 rounded-xl p-4">
                  <h4 className="font-bold text-info mb-3 font-heliopolis">بيانات الاستهداف المحددة</h4>
                  <p className="font-heliopolis text-neutral-80">
                    الوحدة المهاجمة: {battleUnits.find(u => u.id === targetingData.attackerUnitId)?.name}
                  </p>
                  <p className="font-heliopolis text-neutral-80">
                    الهدف: {battleUnits.find(u => u.id === targetingData.targetUnitId)?.name}
                  </p>
                  <p className="font-heliopolis text-neutral-80">
                    دقة الاستهداف: {targetingData.accuracy}%
                  </p>
                </div>
              )}

              <div className="text-center">
                <button
                  onClick={startBattle}
                  className="bg-gradient-primary text-white px-8 py-3 rounded-xl font-heliopolis font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
                >
                  🚀 بدء المعركة
                </button>
              </div>
            </div>
          )}

          {phase === 'battle' && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-xl font-bold font-heliopolis text-neutral-90 mb-2">
                  المعركة جارية...
                </h3>
                <p className="text-neutral-70 font-heliopolis">
                  الوقت المنقضي: {Math.floor(battleTime / 60)}:{(battleTime % 60).toString().padStart(2, '0')}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gradient-to-r from-success/10 to-success/5 rounded-xl p-4">
                  <h4 className="font-bold text-success mb-3 font-heliopolis">حالة القوات الصديقة</h4>
                  <div className="space-y-2">
                    {battleUnits.filter(u => u.side === 'friendly').map(unit => (
                      <div key={unit.id} className="bg-white/50 rounded-lg p-2">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-heliopolis text-sm">{unit.name}</span>
                          <span className={`text-xs px-2 py-1 rounded ${
                            unit.status === 'active' ? 'bg-success text-white' :
                            unit.status === 'damaged' ? 'bg-warning text-white' : 'bg-error text-white'
                          }`}>
                            {unit.status === 'active' ? 'نشط' : unit.status === 'damaged' ? 'متضرر' : 'مدمر'}
                          </span>
                        </div>
                        <div className="w-full bg-neutral-20 rounded-full h-2">
                          <div 
                            className="bg-success h-2 rounded-full transition-all"
                            style={{ width: `${unit.health}%` }}
                          ></div>
                        </div>
                        <div className="flex justify-between text-xs text-neutral-60 mt-1">
                          <span>صحة: {Math.round(unit.health)}%</span>
                          <span>ذخيرة: {unit.ammunition}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-gradient-to-r from-error/10 to-error/5 rounded-xl p-4">
                  <h4 className="font-bold text-error mb-3 font-heliopolis">حالة القوات المعادية</h4>
                  <div className="space-y-2">
                    {battleUnits.filter(u => u.side === 'enemy').map(unit => (
                      <div key={unit.id} className="bg-white/50 rounded-lg p-2">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-heliopolis text-sm">{unit.name}</span>
                          <span className={`text-xs px-2 py-1 rounded ${
                            unit.status === 'active' ? 'bg-error text-white' :
                            unit.status === 'damaged' ? 'bg-warning text-white' : 'bg-neutral-60 text-white'
                          }`}>
                            {unit.status === 'active' ? 'نشط' : unit.status === 'damaged' ? 'متضرر' : 'مدمر'}
                          </span>
                        </div>
                        <div className="w-full bg-neutral-20 rounded-full h-2">
                          <div 
                            className="bg-error h-2 rounded-full transition-all"
                            style={{ width: `${unit.health}%` }}
                          ></div>
                        </div>
                        <div className="flex justify-between text-xs text-neutral-60 mt-1">
                          <span>صحة: {Math.round(unit.health)}%</span>
                          <span>ذخيرة: {unit.ammunition}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="bg-neutral-10 rounded-xl p-4">
                <h4 className="font-bold text-neutral-90 mb-3 font-heliopolis">سجل المعركة</h4>
                <div className="max-h-40 overflow-y-auto space-y-1">
                  {battleEvents.slice(-10).map(event => (
                    <div key={event.id} className="text-sm font-heliopolis text-neutral-70 bg-white rounded p-2">
                      <span className="text-neutral-50">[{Math.floor(event.time / 60)}:{(event.time % 60).toString().padStart(2, '0')}]</span> {event.message}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {phase === 'result' && battleResult && (
            <div className="space-y-6 text-center">
              <div className={`rounded-2xl p-6 ${
                battleResult.winner === 'friendly' ? 'bg-gradient-to-r from-success/20 to-success/10' :
                battleResult.winner === 'enemy' ? 'bg-gradient-to-r from-error/20 to-error/10' :
                'bg-gradient-to-r from-warning/20 to-warning/10'
              }`}>
                <h3 className={`text-2xl font-bold font-heliopolis mb-4 ${
                  battleResult.winner === 'friendly' ? 'text-success' :
                  battleResult.winner === 'enemy' ? 'text-error' : 'text-warning'
                }`}>
                  {battleResult.winner === 'friendly' ? '🎉 انتصار!' :
                   battleResult.winner === 'enemy' ? '💥 هزيمة' : '⚖️ تعادل'}
                </h3>
                <p className="text-lg font-heliopolis text-neutral-80 mb-4">
                  {battleResult.summary}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white rounded-xl p-4 shadow-lg">
                  <h4 className="font-bold text-neutral-90 font-heliopolis mb-2">مدة المعركة</h4>
                  <p className="text-2xl font-bold text-primary-600">
                    {Math.floor(battleResult.duration / 60)}:{(battleResult.duration % 60).toString().padStart(2, '0')}
                  </p>
                </div>
                <div className="bg-white rounded-xl p-4 shadow-lg">
                  <h4 className="font-bold text-neutral-90 font-heliopolis mb-2">خسائر قواتنا</h4>
                  <p className="text-2xl font-bold text-success">{battleResult.friendlyLosses}%</p>
                </div>
                <div className="bg-white rounded-xl p-4 shadow-lg">
                  <h4 className="font-bold text-neutral-90 font-heliopolis mb-2">خسائر العدو</h4>
                  <p className="text-2xl font-bold text-error">{battleResult.enemyLosses}%</p>
                </div>
              </div>

              <button
                onClick={onClose}
                className="bg-gradient-primary text-white px-8 py-3 rounded-xl font-heliopolis font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
              >
                إغلاق المحاكاة
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
